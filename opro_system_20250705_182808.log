2025-07-05 18:28:08,096 - __main__ - INFO - ====================================================================================================
2025-07-05 18:28:08,097 - __main__ - INFO - OPRO系统启动
2025-07-05 18:28:08,097 - __main__ - INFO - ====================================================================================================
2025-07-05 18:28:08,097 - __main__ - INFO - 运行模式: evaluation
2025-07-05 18:28:08,097 - __main__ - INFO - LLM提供商: zhipuai
2025-07-05 18:28:08,097 - __main__ - INFO - OPRO启用: True
2025-07-05 18:28:08,097 - __main__ - INFO - 数据存储启用: True
2025-07-05 18:28:08,097 - __main__ - INFO - 代理日志记录启用: True
2025-07-05 18:28:08,098 - __main__ - INFO - 初始化系统...
2025-07-05 18:28:08,098 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 18:28:08,475 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 18:28:08,475 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-05 18:28:08,477 - __main__ - INFO - 设置实验日期: 2025-07-05
2025-07-05 18:28:08,477 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-05)
2025-07-05 18:28:08,488 - __main__ - INFO - 数据库初始化完成
2025-07-05 18:28:08,490 - __main__ - INFO - 自动备份线程已启动
2025-07-05 18:28:08,490 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 18:28:08,490 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-05 18:28:08,491 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-05 18:28:08,493 - __main__ - INFO - 加载了 0 个智能体的提示词历史
2025-07-05 18:28:08,493 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 18:28:08,493 - __main__ - INFO - 可视化管理器初始化完成
2025-07-05 18:28:08,501 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-05 18:28:08,501 - __main__ - INFO - A/B测试框架初始化完成
2025-07-05 18:28:08,501 - __main__ - INFO - 数据分析工具初始化完成
2025-07-05 18:28:08,502 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-05 18:28:08,502 - __main__ - INFO - 备份管理器初始化完成
2025-07-05 18:28:08,502 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-05 18:28:08,503 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-05 18:28:08,503 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-05 18:28:08,503 - __main__ - INFO - 分析缓存初始化完成
2025-07-05 18:28:08,503 - __main__ - INFO - 联盟管理器初始化完成
2025-07-05 18:28:08,503 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 18:28:08,504 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-05 18:28:08,505 - __main__ - INFO - 数据库初始化完成
2025-07-05 18:28:08,506 - __main__ - INFO - 自动备份线程已启动
2025-07-05 18:28:08,506 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 18:28:08,507 - __main__ - INFO - 加载了 0 个智能体的提示词历史
2025-07-05 18:28:08,508 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 18:28:08,508 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 18:28:08,971 - __main__ - ERROR - 创建数据备份失败: [('data/trading\\2025-01-02\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-02\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-07-05\\assessment_20250705_180615.json', 'data/backups\\backup_20250705_182808\\trading\\2025-07-05\\assessment_20250705_180615.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-05 18:28:08,972 - __main__ - ERROR - 创建数据备份失败: [('data/trading\\2025-01-02\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-02\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-02\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-02\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-02\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-02\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-03\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-06\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-07\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-08\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-09\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-10\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-13\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-14\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-15\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-16\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-17\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-20\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-21\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-22\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-23\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-24\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-27\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\FAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TRA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-28\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\TRA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-29\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BeOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-30\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BeOA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BeOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BOA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\FAA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\FAA\\prompts.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NAA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NOA\\inputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\TRA\\outputs.json', 'data/backups\\backup_20250705_182808\\trading\\2025-01-31\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-05 18:28:08,999 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 18:28:09,018 - __main__ - INFO - 数据库初始化完成
2025-07-05 18:28:09,123 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-05 18:28:09,124 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-05 18:28:09,124 - __main__ - INFO - OPRO优化器初始化完成
2025-07-05 18:28:09,124 - __main__ - INFO - OPRO组件初始化成功
2025-07-05 18:28:09,124 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-05 18:28:09,124 - __main__ - INFO - 系统初始化完成
2025-07-05 18:28:09,124 - __main__ - INFO - ================================================================================
2025-07-05 18:28:09,124 - __main__ - INFO - 运行模式: 标准评估
2025-07-05 18:28:09,124 - __main__ - INFO - ================================================================================
2025-07-05 18:28:09,124 - __main__ - INFO - 运行快速测试...
2025-07-05 18:28:09,124 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 NAA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 TAA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 FAA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 BOA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 BeOA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 NOA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - 智能体 TRA 初始化完成
2025-07-05 18:28:09,131 - __main__ - INFO - [SUCCESS] 成功创建默认LLM智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 18:28:09,131 - __main__ - INFO - 开始贡献度评估流程
2025-07-05 18:28:09,131 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'TRA']
2025-07-05 18:28:09,131 - __main__ - INFO - 可用智能体实例: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 18:28:09,131 - __main__ - INFO - ==================================================
2025-07-05 18:28:09,131 - __main__ - INFO - 阶段1: 分析缓存
2025-07-05 18:28:09,132 - __main__ - INFO - 使用智能体实例进行分析...
2025-07-05 18:28:09,132 - __main__ - INFO - 开始分析缓存阶段...
2025-07-05 18:28:09,132 - __main__ - INFO - 开始填充分析缓存，共 7 个智能体
2025-07-05 18:28:09,132 - __main__ - INFO - 分析缓存已清空
2025-07-05 18:28:09,132 - __main__ - INFO - 执行分析智能体: NAA
2025-07-05 18:28:09,135 - __main__ - INFO - ================================================================================
2025-07-05 18:28:09,135 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 18:28:09,135 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:09,136 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供新闻摘要和关键事件

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:09,136 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:16,379 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:16,389 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 18:28:16,389 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:16,390 - __main__ - INFO - {'content': '```json\n{\n  "analysis_date": "2025-01-01",\n  "analysis_period": "2025-01-01 to 2025-01-03",\n  "available_cash": 100000.00,\n  "stock_symbol": "AAPL", // Assuming the target stock is Apple Inc.\n  "sentiment": 0.6,\n  "summary": "The recent market news has been largely positive for Apple Inc. with strong rumors about the upcoming iPhone 17 release and strong earnings reports from other tech giants which have bolstered investor confidence in the sector.",\n  "key_events": [\n    {\n      "event": "iPhone 17 Rumors",\n      "description": "Heads of technology news outlets have reported that the next iteration of the iPhone, the iPhone 17, is expected to have significant upgrades and could drive consumer demand."\n    },\n    {\n      "event": "Tech Giant Earnings",\n      "description": "Major tech companies like Google and Microsoft have reported record-breaking earnings, boosting the confidence of investors in the technology sector."\n    },\n    {\n      "event": "Apple\'s Supply Chain Update",\n      "description": "Apple has announced an update on its supply chain which has shown improvements, potentially leading to better production and delivery of products."\n    }\n  ],\n  "impact_assessment": {\n    "short_term": "Positive",\n    "medium_term": "Positive",\n    "long_term": "Positive"\n  },\n  "confidence": 0.9\n}\n```', 'type': 'text_response'}
2025-07-05 18:28:16,391 - __main__ - INFO - ================================================================================
2025-07-05 18:28:16,393 - __main__ - INFO - 智能体 NAA 执行成功 (7.26s)
2025-07-05 18:28:16,393 - __main__ - INFO - 执行分析智能体: TAA
2025-07-05 18:28:16,395 - __main__ - INFO - ================================================================================
2025-07-05 18:28:16,395 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 18:28:16,395 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:16,395 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 判断当前技术面是看涨、看跌还是中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- technical_score: 技术评分（-1到1）
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:16,397 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:21,070 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:21,072 - __main__ - INFO - 🤖 TAA LLM输出:
2025-07-05 18:28:21,072 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:21,072 - __main__ - INFO - {'analysis_date': '2025-01-01', 'analysis_period': '2025-01-01 to 2025-01-03', 'cash_available': 100000.0, 'trend': 'neutral', 'support_level': 123.45, 'resistance_level': 135.67, 'technical_score': 0.2, 'indicators': {'RSI': {'current_value': 50, 'signal': 'neutral'}, 'MACD': {'signal_line': 12.34, 'histogram': -5.67, 'signal': 'neutral'}, 'Moving_Averages': {'50_day_MA': 130.0, '200_day_MA': 120.0, 'crossover': 'neutral'}}, 'confidence': 0.8}
2025-07-05 18:28:21,072 - __main__ - INFO - ================================================================================
2025-07-05 18:28:21,074 - __main__ - INFO - 智能体 TAA 执行成功 (4.68s)
2025-07-05 18:28:21,074 - __main__ - INFO - 执行分析智能体: FAA
2025-07-05 18:28:21,075 - __main__ - INFO - ================================================================================
2025-07-05 18:28:21,075 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 18:28:21,075 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:21,075 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 分析长期投资价值

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:21,076 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:28,202 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:28,207 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 18:28:28,207 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:28,208 - __main__ - INFO - {'analysis_date': '2025-01-01', 'analysis_period': '2025-01-01 to 2025-01-03', 'available_cash': '$100,000.00', 'valuation': 'undervalued', 'financial_health': {'rating': 8, 'reasoning': 'The company has a strong current ratio and a healthy profit margin, indicating good liquidity and profitability. The debt-to-equity ratio is low, suggesting minimal financial leverage.'}, 'competitive_position': {'strengths': 'The company holds a significant market share in its industry, strong brand recognition, and a loyal customer base. It has a competitive cost structure and a robust product portfolio.', 'weaknesses': 'The company faces some challenges from new entrants in the market and has a moderate level of product differentiation.'}, 'long_term_outlook': {'growth_potential': 'The industry is expected to grow at a moderate pace, and the company is well-positioned to take advantage of this trend. It has a strong pipeline of new products and is investing in R&D.'}, 'intrinsic_value_estimate': {'estimated_value': '$120,000.00', 'methodology': "The intrinsic value was estimated using a discounted cash flow (DCF) model, considering the company's expected future cash flows and a discount rate of 8%."}, 'confidence': 0.95}
2025-07-05 18:28:28,209 - __main__ - INFO - ================================================================================
2025-07-05 18:28:28,211 - __main__ - INFO - 智能体 FAA 执行成功 (7.14s)
2025-07-05 18:28:28,211 - __main__ - INFO - 执行分析智能体: BOA
2025-07-05 18:28:28,213 - __main__ - INFO - ================================================================================
2025-07-05 18:28:28,213 - __main__ - INFO - 🤖 BOA LLM输入:
2025-07-05 18:28:28,213 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:28,214 - __main__ - INFO - 你是一个看涨倾向的市场分析师，专门构建乐观的市场展望。

你的任务是：
1. 基于前序分析师的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 分析看涨情景下的风险因素

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bullish）
- bullish_factors: 看涨因素列表
- target_price: 目标价位
- upside_potential: 上涨潜力（百分比）
- time_horizon: 时间框架
- risk_factors: 风险因素
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:28,214 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:36,291 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:36,293 - __main__ - INFO - 🤖 BOA LLM输出:
2025-07-05 18:28:36,294 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:36,294 - __main__ - INFO - {'outlook': 'bullish', 'bullish_factors': [{'factor': 'Economic Growth', 'detail': 'The global economy is expected to grow at a robust pace, driven by emerging markets and a recovery in developed economies.'}, {'factor': 'Corporate Earnings', 'detail': 'Corporate earnings are projected to rise significantly, supported by increased business activity and cost-cutting measures.'}, {'factor': 'Interest Rates', 'detail': 'Central banks are expected to maintain accommodative monetary policies, keeping interest rates low and supporting borrowing costs.'}, {'factor': 'Technological Advancements', 'detail': 'Innovation in technology sectors is expected to drive growth, with new products and services creating demand.'}, {'factor': 'Political Stability', 'detail': 'Increased political stability in key economies is expected to reduce uncertainty and boost investor confidence.'}], 'target_price': 150000.0, 'upside_potential': 50.0, 'time_horizon': '12 months', 'risk_factors': [{'risk': 'Economic Slowdown', 'detail': 'A sudden economic slowdown could lead to reduced corporate earnings and investor confidence.'}, {'risk': 'Political Instability', 'detail': 'Political tensions or instability in key economies could disrupt global trade and investment.'}, {'risk': 'Market Volatility', 'detail': 'Market volatility could lead to sudden price swings and increased uncertainty.'}, {'risk': 'Regulatory Changes', 'detail': 'Unexpected regulatory changes could impact certain sectors, potentially leading to negative financial impacts.'}], 'confidence': 0.85, 'analysis_date': '2025-01-01', 'analysis_period': '2025-01-01 to 2025-01-03', 'available_cash': 100000.0}
2025-07-05 18:28:36,295 - __main__ - INFO - ================================================================================
2025-07-05 18:28:36,299 - __main__ - INFO - 智能体 BOA 执行成功 (8.09s)
2025-07-05 18:28:36,299 - __main__ - INFO - 执行分析智能体: BeOA
2025-07-05 18:28:36,301 - __main__ - INFO - ================================================================================
2025-07-05 18:28:36,301 - __main__ - INFO - 🤖 BeOA LLM输入:
2025-07-05 18:28:36,302 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:36,302 - __main__ - INFO - 你是一个看跌倾向的市场分析师，专门构建谨慎的市场展望。

你的任务是：
1. 基于前序分析师的结果，识别和强调风险因素
2. 构建看跌的市场叙述和逻辑
3. 评估下跌风险和支撑位
4. 分析看跌情景下的防御策略

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bearish）
- bearish_factors: 看跌因素列表
- downside_target: 下跌目标
- downside_risk: 下跌风险（百分比）
- support_levels: 关键支撑位
- defensive_strategies: 防御策略
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:36,303 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:45,561 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:45,563 - __main__ - INFO - 🤖 BeOA LLM输出:
2025-07-05 18:28:45,563 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:45,563 - __main__ - INFO - {'content': '```json\n{\n  "outlook": "bearish",\n  "bearish_factors": [\n    "Economic Slowdown": "Indicators suggest a potential economic slowdown, which can negatively impact market confidence.",\n    "Inflation Concerns": "Persistent inflation rates may lead to higher interest rates, reducing consumer spending and corporate investments.",\n    "Geopolitical Tensions": "Increased geopolitical tensions could disrupt global supply chains and affect trade flows.",\n    "Corporate Earnings Woes": "Weakening earnings reports from major companies may signal broader economic challenges.",\n    "Technological Market Saturation": "Some sectors, like technology, may be approaching market saturation, leading to reduced growth prospects."\n  ],\n  "downside_target": {\n    "short_term": "5%",\n    "medium_term": "10%",\n    "long_term": "15%"\n  },\n  "downside_risk": {\n    "high": "30%",\n    "medium": "20%",\n    "low": "10%"\n  },\n  "support_levels": {\n    "immediate": "Current market level - 3%",\n    "intermediate": "50-day moving average",\n    "long_term": "200-day moving average"\n  },\n  "defensive_strategies": [\n    "Diversification": "Spread investments across different asset classes to reduce risk.",\n    "Quality over Quantity": "Focus on companies with strong fundamentals and stable cash flows.",\n    "Hedging": "Use derivatives to protect against potential market declines.",\n    "Cash Reserves": "Maintain a cash reserve to take advantage of market downturns."\n  ],\n  "confidence": 0.85,\n  "analysis_date": "2025-01-01",\n  "analysis_period": "2025-01-01 to 2025-01-03",\n  "available_cash": "$100,000.00"\n}\n```', 'type': 'text_response'}
2025-07-05 18:28:45,563 - __main__ - INFO - ================================================================================
2025-07-05 18:28:45,564 - __main__ - INFO - 智能体 BeOA 执行成功 (9.27s)
2025-07-05 18:28:45,565 - __main__ - INFO - 执行分析智能体: NOA
2025-07-05 18:28:45,566 - __main__ - INFO - ================================================================================
2025-07-05 18:28:45,566 - __main__ - INFO - 🤖 NOA LLM输入:
2025-07-05 18:28:45,566 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:45,566 - __main__ - INFO - 你是一个中性客观的市场观察员，专门提供平衡的市场分析。

你的任务是：
1. 客观平衡地分析看涨和看跌因素
2. 识别市场中的不确定性和矛盾信号
3. 提供中性的观察和等待策略
4. 分析何时市场可能出现明确方向

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（neutral）
- balanced_analysis: 平衡分析
- uncertainty_factors: 不确定性因素
- key_catalysts: 关键催化剂
- wait_and_see_strategy: 观望策略
- market_inefficiencies: 市场无效性
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:45,566 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:57,894 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:28:57,897 - __main__ - INFO - 🤖 NOA LLM输出:
2025-07-05 18:28:57,897 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:57,897 - __main__ - INFO - {'outlook': 'neutral', 'balanced_analysis': {'bullish_factors': {'economic_growth': 'Global economic indicators suggest a stable recovery, which may support market growth.', 'corporate_earnings': 'Many companies have reported strong earnings, indicating a positive outlook for the market.', 'monetary_policy': 'Central banks have been supportive with low interest rates, which may boost investment.'}, 'bearish_factors': {'inflation_concerns': 'Rising inflation in some regions could lead to higher borrowing costs and reduce consumer spending.', 'geopolitical_tensions': 'Increased geopolitical tensions may create uncertainty and affect global markets.', 'technical_indicators': 'Market technical indicators show some signs of overbought conditions, which may lead to corrections.'}}, 'uncertainty_factors': {'inflation': 'The trajectory of inflation remains uncertain, as it could rise faster than expected.', 'central_bank_policy': 'Central bank policy decisions, especially rate hikes, can have significant market impacts.', 'COVID-19': 'The ongoing impact of the COVID-19 pandemic cannot be overlooked, as it continues to affect consumer behavior and business operations.'}, 'key_catalysts': {'economic_data': 'Upcoming economic reports and data releases could provide clear direction for the market.', 'central_bank_statements': 'Central bank speeches and policy statements can influence market sentiment.', 'earnings季': 'Company earnings reports during the earnings season can drive market movements.'}, 'wait_and_see_strategy': {'monitor_indicators': 'Keep an eye on economic indicators, inflation rates, and central bank statements.', 'avoid_impulsive_trades': 'Resist the temptation to make impulsive trades based on short-term market movements.', 'consider_diversification': 'Diversify your portfolio to manage risk and exposure to market volatility.'}, 'market_inefficiencies': {'momentum_trading': 'Momentum trading can lead to mispriced assets and market inefficiencies.', 'news_impact': 'Market reactions to news can sometimes be exaggerated and not necessarily reflect fundamental values.'}, 'confidence': 0.65, 'analysis_date': '2025-01-01', 'analysis_period': '2025-01-01 to 2025-01-03', 'available_cash': '$100,000.00'}
2025-07-05 18:28:57,897 - __main__ - INFO - ================================================================================
2025-07-05 18:28:57,899 - __main__ - INFO - 智能体 NOA 执行成功 (12.33s)
2025-07-05 18:28:57,900 - __main__ - INFO - 执行分析智能体: TRA
2025-07-05 18:28:57,901 - __main__ - INFO - ================================================================================
2025-07-05 18:28:57,902 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 18:28:57,902 - __main__ - INFO - ----------------------------------------
2025-07-05 18:28:57,902 - __main__ - INFO - 你是一个专业的交易员，负责做出最终的交易决策。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析  
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

基于以上所有信息，做出具体的交易决策。

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0到1，1表示全仓）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

注意：
- 如果信号冲突严重，选择持有(hold)
- 考虑风险管理和仓位控制
- 基于多个分析师的共识程度调整信心度

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-03
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:28:57,903 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:05,541 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:05,542 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 18:29:05,542 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:05,542 - __main__ - INFO - {'action': 'hold', 'position_size': 0.5, 'reasoning': {'NAA': 'Market sentiment analysis indicates a stable mood with a slight bias towards neutral, with no strong bearish or bullish trends.', 'TAA': 'Technical analysis shows mixed signals with both support and resistance levels being tested, suggesting consolidation rather than a strong trend.', 'FAA': 'Fundamental analysis indicates fair value, with slight overvaluation in the short term but solid long-term prospects.', 'BOA': 'Bullish analysts are optimistic about the medium-term growth potential, although they note that current valuations are not extremely attractive.', 'BeOA': 'Bearish analysts warn of potential risks in the market, particularly in the event of economic downturns or unexpected news.', 'NOA': 'Neutral observers believe the market is balanced, with neither significant upside nor downside risks expected in the near term.'}, 'risk_assessment': {'market_risk': 'Low', 'specific_risk': 'Moderate', 'overall_risk': 'Moderate'}, 'stop_loss': {'level': None, 'reason': 'Given the mixed signals and fair value assessment, a stop loss is not deemed necessary at this time.'}, 'take_profit': {'level': None, 'reason': 'With a neutral to slightly bullish outlook, a take profit level is not set as there is no strong indication of a significant upward move.'}, 'time_horizon': 'medium-term', 'confidence': 0.7}
2025-07-05 18:29:05,543 - __main__ - INFO - ================================================================================
2025-07-05 18:29:05,545 - __main__ - INFO - 智能体 TRA 执行成功 (7.64s)
2025-07-05 18:29:05,545 - __main__ - INFO - 分析缓存填充完成: 成功 7 个, 失败 0 个, 总耗时 56.41s
2025-07-05 18:29:05,545 - __main__ - INFO - 分析缓存阶段完成: 成功缓存 7 个智能体
2025-07-05 18:29:05,545 - __main__ - INFO - ==================================================
2025-07-05 18:29:05,545 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-05 18:29:05,546 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-05 18:29:05,546 - __main__ - INFO - 使用配置的simulation_days: 2
2025-07-05 18:29:05,546 - __main__ - INFO - 总交易天数: 2, 计划交易周数: 1
2025-07-05 18:29:05,546 - __main__ - INFO - ============================================================
2025-07-05 18:29:05,546 - __main__ - INFO - 第 1 周交易 (第 1-2 天)
2025-07-05 18:29:05,546 - __main__ - INFO - ============================================================
2025-07-05 18:29:05,546 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-05 18:29:05,546 - __main__ - INFO - 开始联盟生成阶段...
2025-07-05 18:29:05,546 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-05 18:29:05,547 - __main__ - INFO - 总智能体数: 4
2025-07-05 18:29:05,547 - __main__ - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-05 18:29:05,547 - __main__ - INFO - 交易智能体: TRA
2025-07-05 18:29:05,548 - __main__ - INFO - 生成了 16 个初始联盟
2025-07-05 18:29:05,549 - __main__ - INFO - 联盟剪枝完成:
2025-07-05 18:29:05,549 - __main__ - INFO -   - 总联盟数: 16
2025-07-05 18:29:05,549 - __main__ - INFO -   - 有效联盟: 7
2025-07-05 18:29:05,549 - __main__ - INFO -   - 剪枝联盟: 9
2025-07-05 18:29:05,549 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-05 18:29:05,549 - __main__ - INFO -   - 生成耗时: 0.003s
2025-07-05 18:29:05,549 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 7 个，剪枝联盟 9 个
2025-07-05 18:29:05,550 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (2 天)
2025-07-05 18:29:05,550 - __main__ - INFO - 开始交易模拟阶段...
2025-07-05 18:29:05,550 - __main__ - INFO - 限制模拟联盟数量: 7 -> 3
2025-07-05 18:29:05,550 - __main__ - INFO - 使用串行模拟：3 个联盟
2025-07-05 18:29:05,550 - __main__ - INFO - 模拟联盟 1/3: {'FAA', 'TAA', 'TRA'}
2025-07-05 18:29:05,550 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TAA', 'TRA'}
2025-07-05 18:29:05,790 - __main__ - INFO - 🤖 执行智能体 TAA (使用LLM)
2025-07-05 18:29:05,835 - __main__ - INFO - ================================================================================
2025-07-05 18:29:05,836 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 18:29:05,836 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:05,837 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 判断当前技术面是看涨、看跌还是中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- technical_score: 技术评分（-1到1）
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:05,838 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:10,943 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:10,947 - __main__ - INFO - 🤖 TAA LLM输出:
2025-07-05 18:29:10,947 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:10,948 - __main__ - INFO - {'analysis_date': '2025-01-02', 'available_cash': 100000.0, 'analysis_result': {'trend': 'bullish', 'support_level': 95.0, 'resistance_level': 105.0, 'technical_score': 0.8, 'indicators': {'RSI': {'current_value': 68, 'interpretation': 'Overbought but approaching neutral'}, 'MACD': {'signal_line': 0.1, 'histogram': {'current_value': 0.02, 'trend': 'positive'}, 'interpretation': 'MACD signal line above zero indicates a bullish trend'}, 'moving_averages': {'50_day_ma': 100.0, '200_day_ma': 98.0, 'interpretation': 'Price above the 50-day and 200-day moving averages, suggesting a strong bullish trend'}}, 'confidence': 0.95}}
2025-07-05 18:29:10,949 - __main__ - INFO - ================================================================================
2025-07-05 18:29:10,959 - __main__ - INFO - 🤖 执行智能体 FAA (使用LLM)
2025-07-05 18:29:11,005 - __main__ - INFO - ================================================================================
2025-07-05 18:29:11,006 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 18:29:11,006 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:11,006 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 分析长期投资价值

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

🤖 前序智能体分析:
  • TAA: {'analysis_date': '2025-01-02', 'available_cash': 100000.0, 'analysis_result': {'trend': 'bullish', ...

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:11,008 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:12,791 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:12,792 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 18:29:12,792 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:12,792 - __main__ - INFO - {'analysis_date': '2025-01-02', 'valuation': 'fair', 'financial_health': 8, 'competitive_position': 'strong', 'long_term_outlook': 'positive', 'intrinsic_value_estimate': 120000.0, 'confidence': 0.95}
2025-07-05 18:29:12,792 - __main__ - INFO - ================================================================================
2025-07-05 18:29:12,796 - __main__ - INFO - 🤖 执行智能体 TRA (使用LLM)
2025-07-05 18:29:12,879 - __main__ - INFO - ================================================================================
2025-07-05 18:29:12,880 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 18:29:12,880 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:12,880 - __main__ - INFO - 你是一个专业的交易员，负责做出最终的交易决策。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析  
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

基于以上所有信息，做出具体的交易决策。

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0到1，1表示全仓）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

注意：
- 如果信号冲突严重，选择持有(hold)
- 考虑风险管理和仓位控制
- 基于多个分析师的共识程度调整信心度

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

🤖 前序智能体分析:
  • TAA: {'analysis_date': '2025-01-02', 'available_cash': 100000.0, 'analysis_result': {'trend': 'bullish', ...
  • FAA: {'analysis_date': '2025-01-02', 'valuation': 'fair', 'financial_health': 8, 'competitive_position': ...

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:12,881 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:19,983 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:19,985 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 18:29:19,985 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:19,986 - __main__ - INFO - {'action': 'hold', 'position_size': 0.5, 'reasoning': {'NAA': 'Market sentiment analysis indicates a mixed sentiment with both optimism and caution.', 'TAA': 'Technical analysis suggests a bullish trend but with some signs of consolidation.', 'FAA': 'Valuation analysis indicates fair value with moderate financial health and competitive position.', 'BOA': 'Bullish analysts are optimistic about future growth prospects.', 'BeOA': 'Bearish analysts caution about potential risks and market volatility.', 'NOA': 'Neutral observers see both opportunities and risks, suggesting a balanced view.'}, 'risk_assessment': {'market_risk': 'Moderate', 'liquidity_risk': 'Low', 'counterparty_risk': 'Low', 'operational_risk': 'Low'}, 'stop_loss': {'level': 'Not applicable at this stage', 'reason': 'Market is in a consolidation phase with no clear trend to justify a stop loss.'}, 'take_profit': {'level': 'Not applicable at this stage', 'reason': 'Market is in a consolidation phase and it is too early to set a take profit level.'}, 'time_horizon': 'Medium-term', 'confidence': 0.7}
2025-07-05 18:29:19,987 - __main__ - INFO - ================================================================================
2025-07-05 18:29:19,995 - __main__ - INFO - 📊 交易决策: hold (信心度: 0.70)
2025-07-05 18:29:19,995 - __main__ - INFO - 📝 决策理由: {'NAA': 'Market sentiment analysis indicates a mixed sentiment with both optimism and caution.', 'TAA': 'Technical analysis suggests a bullish trend but with some signs of consolidation.', 'FAA': 'Valuation analysis indicates fair value with moderate financial health and competitive position.', 'BOA': 'Bullish analysts are optimistic about future growth prospects.', 'BeOA': 'Bearish analysts caution about potential risks and market volatility.', 'NOA': 'Neutral observers see both opportunities and risks, suggesting a balanced view.'}
2025-07-05 18:29:19,997 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-05 18:29:19,997 - __main__ - INFO - ============================================================
2025-07-05 18:29:19,997 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TAA', 'TRA'}
2025-07-05 18:29:19,997 - __main__ - INFO - 周总收益率: 0.0000
2025-07-05 18:29:19,997 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-05 18:29:19,998 - __main__ - INFO - 交易天数: 1
2025-07-05 18:29:19,998 - __main__ - INFO - ============================================================
2025-07-05 18:29:19,998 - __main__ - INFO - 联盟 {'FAA', 'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-05 18:29:19,998 - __main__ - INFO - 模拟联盟 2/3: {'FAA', 'TRA'}
2025-07-05 18:29:19,998 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA'}
2025-07-05 18:29:20,029 - __main__ - INFO - 🤖 执行智能体 FAA (使用LLM)
2025-07-05 18:29:20,074 - __main__ - INFO - ================================================================================
2025-07-05 18:29:20,074 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 18:29:20,074 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:20,074 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 分析长期投资价值

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:20,075 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:23,346 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:23,347 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 18:29:23,347 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:23,347 - __main__ - INFO - {'analysis_date': '2025-01-02', 'available_cash': '$100,000.00', 'valuation': 'undervalued', 'financial_health': 8.5, 'competitive_position': {'industry_ranking': 'top 3', 'market_share': '15%', 'brand_recognition': 'high', 'innovation': 'above average'}, 'long_term_outlook': 'positive', 'intrinsic_value_estimate': '$200,000.00', 'confidence': 0.95}
2025-07-05 18:29:23,347 - __main__ - INFO - ================================================================================
2025-07-05 18:29:23,350 - __main__ - INFO - 🤖 执行智能体 TRA (使用LLM)
2025-07-05 18:29:23,427 - __main__ - INFO - ================================================================================
2025-07-05 18:29:23,427 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 18:29:23,428 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:23,428 - __main__ - INFO - 你是一个专业的交易员，负责做出最终的交易决策。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析  
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

基于以上所有信息，做出具体的交易决策。

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0到1，1表示全仓）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

注意：
- 如果信号冲突严重，选择持有(hold)
- 考虑风险管理和仓位控制
- 基于多个分析师的共识程度调整信心度

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

🤖 前序智能体分析:
  • FAA: {'analysis_date': '2025-01-02', 'available_cash': '$100,000.00', 'valuation': 'undervalued', 'financ...

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:23,428 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:32,873 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:32,875 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 18:29:32,875 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:32,875 - __main__ - INFO - {'content': '```json\n{\n  "action": "hold",\n  "position_size": 0.5,\n  "reasoning": {\n    "NAA": "Market sentiment is neutral with a slight bullish trend.",\n    "TAA": "Technical analysis indicates a strong support level and a bullish trend.",\n    "FAA": "The stock is undervalued according to fundamental analysis.",\n    "BOA": "Bullish analysts are optimistic about the company\'s future growth.",\n    "BeOA": "Doubts about the sustainability of growth are raised by bearish analysts.",\n    "NOA": "Neutral observers see a balanced market with opportunities on both sides."\n  },\n  "risk_assessment": {\n    "market_risk": "Low, as the market sentiment is neutral with a slight bullish trend.",\n    "specific_risk": "Medium, due to uncertainties in the company\'s growth prospects.",\n    "liquidity_risk": "Low, as the stock has good liquidity."\n  },\n  "stop_loss": {\n    "level": "$80.00",\n    "trigger": "If the stock price falls below the support level identified by the TAA."\n  },\n  "take_profit": {\n    "level": "$120.00",\n    "trigger": "If the stock price reaches the resistance level identified by the TAA."\n  },\n  "time_horizon": "medium-term",\n  "confidence": 0.7\n}\n```\n\nIn this decision, the balance of various analysts\' opinions has led to a hold position. The confidence level is set at 0.7, reflecting a moderate level of certainty in the decision. The risk assessment shows that while the market risk is low, there is a medium-level specific risk due to uncertainties in the company\'s growth prospects. The stop-loss and take-profit levels are set based on the technical analysis provided by the TAA.', 'type': 'text_response'}
2025-07-05 18:29:32,877 - __main__ - INFO - ================================================================================
2025-07-05 18:29:32,887 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-05 18:29:32,887 - __main__ - INFO - ============================================================
2025-07-05 18:29:32,887 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA'}
2025-07-05 18:29:32,888 - __main__ - INFO - 周总收益率: 0.0000
2025-07-05 18:29:32,888 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-05 18:29:32,888 - __main__ - INFO - 交易天数: 1
2025-07-05 18:29:32,888 - __main__ - INFO - ============================================================
2025-07-05 18:29:32,888 - __main__ - INFO - 联盟 {'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-05 18:29:32,888 - __main__ - INFO - 模拟联盟 3/3: {'NAA', 'TRA'}
2025-07-05 18:29:32,889 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA'}
2025-07-05 18:29:32,916 - __main__ - INFO - 🤖 执行智能体 NAA (使用LLM)
2025-07-05 18:29:32,951 - __main__ - INFO - ================================================================================
2025-07-05 18:29:32,952 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 18:29:32,952 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:32,952 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供新闻摘要和关键事件

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:32,952 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:38,926 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:38,928 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 18:29:38,928 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:38,928 - __main__ - INFO - {'sentiment': 0.3, 'summary': '今天的市场新闻显示出混合的情绪。一方面，全球经济增长放缓的消息对市场产生了一定程度的悲观情绪；另一方面，科技股的强劲表现和某些行业公司的良好财报发布给市场带来了一丝乐观。整体情绪较为稳定。', 'key_events': [{'event': '全球经济增长放缓', 'impact': '负面影响'}, {'event': '科技股强劲表现', 'impact': '正面影响'}, {'event': '某些行业公司财报发布', 'impact': '正面影响'}], 'impact_assessment': {'target_stock': {'symbol': 'AAPL', 'impact': '中性', 'reason': '尽管整体市场情绪稳定，但具体对AAPL的影响尚需观察后续新闻和财报数据。'}}, 'confidence': 0.8}
2025-07-05 18:29:38,928 - __main__ - INFO - ================================================================================
2025-07-05 18:29:38,933 - __main__ - INFO - 🤖 执行智能体 TRA (使用LLM)
2025-07-05 18:29:39,015 - __main__ - INFO - ================================================================================
2025-07-05 18:29:39,015 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 18:29:39,015 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:39,015 - __main__ - INFO - 你是一个专业的交易员，负责做出最终的交易决策。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析  
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

基于以上所有信息，做出具体的交易决策。

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0到1，1表示全仓）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

注意：
- 如果信号冲突严重，选择持有(hold)
- 考虑风险管理和仓位控制
- 基于多个分析师的共识程度调整信心度

📅 分析日期: 2025-01-02
💵 可用现金: $100,000.00

🤖 前序智能体分析:
  • NAA: 今天的市场新闻显示出混合的情绪。一方面，全球经济增长放缓的消息对市场产生了一定程度的悲观情绪；另一方面，科技股的强劲表现和某些行业公司的良好财报发布给市场带来了一丝乐观。整体情绪较为稳定。

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 18:29:39,015 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:45,785 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 18:29:45,788 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 18:29:45,788 - __main__ - INFO - ----------------------------------------
2025-07-05 18:29:45,789 - __main__ - INFO - {'action': 'hold', 'position_size': 0.5, 'reasoning': {'NAA': '市场情绪稳定，虽有悲观情绪但乐观因素并存。', 'TAA': '技术分析显示市场短期内波动不大，无明确趋势。', 'FAA': '基本面分析显示当前价格与公司价值基本匹配。', 'BOA': '看涨分析师认为市场有进一步上涨的潜力。', 'BeOA': '看跌分析师警告市场存在潜在风险。', 'NOA': '中性观察员认为市场处于平衡状态，无明确方向。'}, 'risk_assessment': {'market_risk': '市场情绪稳定，但潜在风险存在。', 'company_specific_risk': '无特定公司风险。', 'systematic_risk': '全球经济放缓可能影响市场表现。'}, 'stop_loss': {'level': None, 'reason': '市场情绪稳定，无明确趋势，故不设置止损位。'}, 'take_profit': {'level': None, 'reason': '无明确趋势，不设置止盈位。'}, 'time_horizon': 'short-term', 'confidence': 0.7}
2025-07-05 18:29:45,789 - __main__ - INFO - ================================================================================
2025-07-05 18:29:45,798 - __main__ - INFO - 📊 交易决策: hold (信心度: 0.70)
2025-07-05 18:29:45,798 - __main__ - INFO - 📝 决策理由: {'NAA': '市场情绪稳定，虽有悲观情绪但乐观因素并存。', 'TAA': '技术分析显示市场短期内波动不大，无明确趋势。', 'FAA': '基本面分析显示当前价格与公司价值基本匹配。', 'BOA': '看涨分析师认为市场有进一步上涨的潜力。', 'BeOA': '看跌分析师警告市场存在潜在风险。', 'NOA': '中性观察员认为市场处于平衡状态，无明确方向。'}
2025-07-05 18:29:45,799 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-05 18:29:45,799 - __main__ - INFO - ============================================================
2025-07-05 18:29:45,799 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA'}
2025-07-05 18:29:45,799 - __main__ - INFO - 周总收益率: 0.0000
2025-07-05 18:29:45,800 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-05 18:29:45,800 - __main__ - INFO - 交易天数: 1
2025-07-05 18:29:45,800 - __main__ - INFO - ============================================================
2025-07-05 18:29:45,800 - __main__ - INFO - 联盟 {'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-05 18:29:45,800 - __main__ - INFO - 串行交易模拟完成: 成功 3 个，失败 0 个
2025-07-05 18:29:45,800 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-05 18:29:45,800 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 18:29:45,800 - __main__ - INFO - 开始计算 4 个智能体的Shapley值
2025-07-05 18:29:45,800 - __main__ - INFO - 已提供 3 个联盟的特征函数值
2025-07-05 18:29:45,800 - __main__ - INFO - 联盟值补全完成: 已提供 3 个，补全 13 个
2025-07-05 18:29:45,801 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-05 18:29:45,801 - __main__ - INFO - Shapley值计算阶段完成: 计算了 4 个智能体的贡献度
2025-07-05 18:29:45,801 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-05 18:29:45,801 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-05 18:29:45,801 - __main__ - INFO - 第 1 周完成: 联盟 7 个, 模拟 3 个
2025-07-05 18:29:45,801 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-05 18:29:45,801 - __main__ - INFO - ==================================================
2025-07-05 18:29:45,801 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-05 18:29:45,801 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 18:29:45,801 - __main__ - INFO - 开始计算 4 个智能体的Shapley值
2025-07-05 18:29:45,801 - __main__ - INFO - 已提供 3 个联盟的特征函数值
2025-07-05 18:29:45,802 - __main__ - INFO - 联盟值补全完成: 已提供 3 个，补全 13 个
2025-07-05 18:29:45,802 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-05 18:29:45,802 - __main__ - INFO - Shapley值计算阶段完成: 计算了 4 个智能体的贡献度
2025-07-05 18:29:45,802 - __main__ - INFO - ==================================================
2025-07-05 18:29:45,802 - __main__ - INFO - 贡献度评估完成，总耗时: 96.68s
2025-07-05 18:29:45,802 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 18:29:45,802 - __main__ - INFO - 开始交易会话: assessment_20250705_182945
2025-07-05 18:29:45,810 - __main__ - INFO - 交易会话数据已存储: assessment_20250705_182945
2025-07-05 18:29:45,810 - __main__ - INFO - 交易会话结束并保存: assessment_20250705_182945 (系统盈亏: 0.00)
2025-07-05 18:29:45,811 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250705_182945
2025-07-05 18:29:45,811 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-05 18:29:45,811 - __main__ - INFO - 评估结果数据处理完成
2025-07-05 18:29:45,811 - __main__ - INFO - ====================================================================================================
2025-07-05 18:29:45,811 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-05 18:29:45,811 - __main__ - INFO - ====================================================================================================
